"""
Project Setup View
==================

View component for project setup and configuration.
"""

import flet as ft
from typing import Dict, Any, Optional

from views.base_view import BaseView
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from components.forms.client_profile_form import ClientProfileForm
from components.forms.project_params_form import ProjectParamsForm


class ProjectSetupView(BaseView):
    """View for project setup and configuration."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.client_profile = ClientProfile()
        self.project_assumptions = EnhancedProjectAssumptions()
        
        # Form components
        self.client_form: Optional[ClientProfileForm] = None
        self.params_form: Optional[ProjectParamsForm] = None
    
    def build_content(self) -> ft.Control:
        """Build the project setup view content."""
        
        # Header
        header = self.create_section_header(
            "Project Setup",
            "Configure client information and project parameters"
        )
        
        # Client Profile Form
        self.client_form = ClientProfileForm(self.client_profile)
        self.client_form.on_data_changed = self._on_client_data_changed
        
        client_card = self.create_card(
            "Client Profile",
            self.client_form.build(),
            icon=ft.Icons.BUSINESS,
            bgcolor=ft.Colors.BLUE_50
        )
        
        # Project Parameters Form
        self.params_form = ProjectParamsForm(self.project_assumptions)
        self.params_form.on_data_changed = self._on_params_data_changed
        
        params_card = self.create_card(
            "Project Parameters",
            self.params_form.build(),
            icon=ft.Icons.SETTINGS
        )
        
        # Action buttons
        action_buttons = self._create_action_buttons()
        
        # Comprehensive report button
        comprehensive_button = self._create_comprehensive_button()
        
        # Validation status
        validation_status = self._create_validation_status()
        
        return ft.Column([
            header,
            client_card,
            params_card,
            validation_status,
            action_buttons,
            comprehensive_button
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_action_buttons(self) -> ft.Row:
        """Create action buttons row."""
        return ft.Row([
            self.create_action_button(
                "Load Preset",
                ft.Icons.UPLOAD_FILE,
                self._on_load_preset,
                ft.Colors.BLUE_600
            ),
            self.create_action_button(
                "Save Configuration",
                ft.Icons.SAVE,
                self._on_save_config,
                ft.Colors.GREEN_600
            ),
            self.create_action_button(
                "Run Model",
                ft.Icons.PLAY_ARROW,
                self._on_run_model,
                ft.Colors.ORANGE_600
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
    
    def _create_comprehensive_button(self) -> ft.Card:
        """Create comprehensive analysis button."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.AUTO_AWESOME, color=ft.Colors.PURPLE_600),
                        ft.Text("Complete Analysis & Report Generation", 
                               size=18, weight=ft.FontWeight.BOLD)
                    ]),
                    ft.Text(
                        "Generate comprehensive analysis including location comparison, "
                        "all charts, and professional reports",
                        size=12, color=ft.Colors.GREY_700
                    ),
                    ft.ElevatedButton(
                        "🚀 Generate Complete Analysis & Reports",
                        icon=ft.Icons.ROCKET_LAUNCH,
                        on_click=self._on_comprehensive_analysis,
                        bgcolor=ft.Colors.PURPLE_600,
                        color=ft.Colors.WHITE,
                        width=400,
                        height=50,
                        style=ft.ButtonStyle(
                            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD)
                        )
                    )
                ]),
                padding=20,
                bgcolor=ft.Colors.PURPLE_50
            )
        )
    
    def _create_validation_status(self) -> ft.Container:
        """Create validation status display."""
        validation_results = self.project_assumptions.get_validation_status()
        
        if validation_results['is_valid']:
            status_color = ft.Colors.GREEN
            status_icon = ft.Icons.CHECK_CIRCLE
            status_text = "Configuration Valid"
        else:
            status_color = ft.Colors.RED
            status_icon = ft.Icons.ERROR
            status_text = f"Validation Issues ({validation_results['error_count']} errors)"
        
        status_content = ft.Row([
            ft.Icon(status_icon, color=status_color),
            ft.Text(status_text, color=status_color, weight=ft.FontWeight.BOLD)
        ])
        
        # Add error details if any
        if not validation_results['is_valid']:
            error_details = ft.Column([
                ft.Text("Validation Errors:", size=14, weight=ft.FontWeight.BOLD),
                *[ft.Text(f"• {error}", size=12, color=ft.Colors.RED_700) 
                  for error in validation_results['errors'].values()]
            ])
            
            status_content = ft.Column([
                status_content,
                ft.Divider(height=10),
                error_details
            ])
        
        # Add warnings if any
        warnings = validation_results.get('warnings', {})
        if warnings:
            warning_details = ft.Column([
                ft.Text("Warnings:", size=14, weight=ft.FontWeight.BOLD),
                *[ft.Text(f"• {warning}", size=12, color=ft.Colors.ORANGE_700) 
                  for warning in warnings.values()]
            ])
            
            if isinstance(status_content, ft.Row):
                status_content = ft.Column([status_content])
            
            status_content.controls.extend([
                ft.Divider(height=10),
                warning_details
            ])
        
        return ft.Container(
            content=status_content,
            padding=15,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            border=ft.border.all(1, status_color)
        )
    
    def _on_client_data_changed(self, field: str, value: Any):
        """Handle client profile data changes."""
        setattr(self.client_profile, field, value)
        self.notify_data_changed("client_profile", self.client_profile)
        self.refresh()  # Refresh to update validation status
    
    def _on_params_data_changed(self, field: str, value: Any):
        """Handle project parameters data changes with auto-save."""
        
        # Get enhanced integration service for auto-save and undo/redo
        try:
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            
            # Record undo command if service available
            if enhanced_service.undo_redo_service:
                from services.undo_redo_service import StateChangeCommand
                old_value = getattr(self.project_assumptions, field, None)
                command = StateChangeCommand(
                    target=self.project_assumptions,
                    property_name=field,
                    old_value=old_value,
                    new_value=value,
                    description=f"Change {field} from {old_value} to {value}"
                )
                enhanced_service.undo_redo_service.execute_command(command)
            
        except Exception as e:
            # Fallback to standard behavior if enhanced service unavailable
            print(f"Enhanced features unavailable: {e}")
        
        # Update data
        setattr(self.project_assumptions, field, value)
        self.project_assumptions.validate_all()  # Re-validate
        self.notify_data_changed("project_assumptions", self.project_assumptions)
        
        # Auto-save if enhanced service available
        try:
            if hasattr(self, '_last_auto_save_time'):
                import time
                if time.time() - self._last_auto_save_time < 2:  # Debounce auto-save
                    return
            
            if enhanced_service.persistence_service and self.client_profile.is_complete():
                project_id = self.client_profile.get_clean_company_name()
                enhanced_service.save_project_with_versioning(
                    project_id=project_id,
                    project_data={
                        'client_profile': self.client_profile.to_dict(),
                        'assumptions': self.project_assumptions.to_dict()
                    }
                )
                self._last_auto_save_time = time.time()
                print(f"Auto-saved project: {project_id}")
        except Exception as e:
            print(f"Auto-save failed: {e}")
        
        self.refresh()  # Refresh to update validation status
    
    def _on_load_preset(self, e):
        """Handle load preset action."""
        self.request_action("load_preset")
    
    def _on_save_config(self, e):
        """Handle save configuration action."""
        # Validate before saving
        if not self.client_profile.is_complete():
            self.show_error("Please complete client profile before saving")
            return
        
        if not self.project_assumptions.is_validated:
            self.show_error("Please fix validation errors before saving")
            return
        
        self.request_action("save_configuration", {
            "client_profile": self.client_profile.to_dict(),
            "project_assumptions": self.project_assumptions.to_dict()
        })
    
    def _on_run_model(self, e):
        """Handle run model action."""
        # Validate before running
        if not self.client_profile.is_complete():
            self.show_error("Please complete client profile before running model")
            return
        
        if not self.project_assumptions.is_validated:
            self.show_error("Please fix validation errors before running model")
            return
        
        self.request_action("run_financial_model", {
            "assumptions": self.project_assumptions.to_dict()
        })
    
    def _on_comprehensive_analysis(self, e):
        """Handle comprehensive analysis action."""
        # Validate before running
        if not self.client_profile.is_complete():
            self.show_error("Please complete client profile before running comprehensive analysis")
            return
        
        if not self.project_assumptions.is_validated:
            self.show_error("Please fix validation errors before running comprehensive analysis")
            return
        
        self.request_action("run_comprehensive_analysis", {
            "client_profile": self.client_profile.to_dict(),
            "assumptions": self.project_assumptions.to_dict()
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new data."""
        if "client_profile" in data:
            self.client_profile = ClientProfile.from_dict(data["client_profile"])
            if self.client_form:
                self.client_form.update_data(self.client_profile)
        
        if "project_assumptions" in data:
            self.project_assumptions = EnhancedProjectAssumptions.from_dict(data["project_assumptions"])
            if self.params_form:
                self.params_form.update_data(self.project_assumptions)
        
        self.refresh()
    
    def get_client_profile(self) -> ClientProfile:
        """Get current client profile."""
        return self.client_profile
    
    def get_project_assumptions(self) -> EnhancedProjectAssumptions:
        """Get current project assumptions."""
        return self.project_assumptions
    
    def set_client_profile(self, client_profile: ClientProfile):
        """Set client profile."""
        self.client_profile = client_profile
        if self.client_form:
            self.client_form.update_data(client_profile)
        self.refresh()
    
    def set_project_assumptions(self, assumptions: EnhancedProjectAssumptions):
        """Set project assumptions."""
        self.project_assumptions = assumptions
        if self.params_form:
            self.params_form.update_data(assumptions)
        self.refresh()
    
    def validate_all(self) -> bool:
        """Validate all form data."""
        client_valid = self.client_profile.is_complete()
        params_valid = self.project_assumptions.is_validated
        
        return client_valid and params_valid
