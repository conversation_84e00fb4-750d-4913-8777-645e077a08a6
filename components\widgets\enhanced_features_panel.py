"""
Enhanced Features Panel Widget
==============================

A comprehensive panel showing status and controls for all enhanced features.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable
from datetime import datetime


class EnhancedFeaturesPanel:
    """Panel widget displaying enhanced features status and controls."""
    
    def __init__(self, 
                 on_feature_toggle: Optional[Callable[[str, bool], None]] = None,
                 on_action: Optional[Callable[[str], None]] = None):
        """Initialize the enhanced features panel.
        
        Args:
            on_feature_toggle: Callback when feature is toggled (feature_name, enabled)
            on_action: Callback for feature actions (action_name)
        """
        self.on_feature_toggle = on_feature_toggle
        self.on_action = on_action
        self.features_status = {}
        self.system_stats = {}
        
    def build(self) -> ft.Container:
        """Build the enhanced features panel."""
        return ft.Container(
            content=ft.Column([
                self._create_header(),
                ft.Divider(height=1),
                self._create_features_grid(),
                ft.Divider(height=1),
                self._create_stats_section(),
                ft.Divider(height=1),
                self._create_actions_section()
            ], spacing=10),
            padding=20,
            bgcolor=ft.Colors.with_opacity(0.95, ft.Colors.WHITE),
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_header(self) -> ft.Container:
        """Create panel header."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.ROCKET_LAUNCH, color=ft.Colors.BLUE_600, size=28),
                ft.Column([
                    ft.Text("Enhanced Features Control Center", 
                           size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_900),
                    ft.Text("AI-Powered Financial Modeling v3.0", 
                           size=12, color=ft.Colors.GREY_600)
                ], spacing=2),
                ft.Container(expand=True),
                ft.IconButton(
                    icon=ft.Icons.SETTINGS,
                    icon_color=ft.Colors.GREY_600,
                    tooltip="Configure Features",
                    on_click=lambda _: self.on_action("configure") if self.on_action else None
                )
            ], alignment=ft.MainAxisAlignment.START),
            padding=ft.padding.only(bottom=10)
        )
    
    def _create_features_grid(self) -> ft.Container:
        """Create features status grid."""
        features = [
            {
                "id": "persistence",
                "name": "Data Persistence",
                "icon": ft.Icons.SAVE,
                "description": "Auto-save & versioning",
                "color": ft.Colors.GREEN
            },
            {
                "id": "caching",
                "name": "Smart Caching",
                "icon": ft.Icons.SPEED,
                "description": "Performance optimization",
                "color": ft.Colors.ORANGE
            },
            {
                "id": "recovery",
                "name": "Error Recovery",
                "icon": ft.Icons.SHIELD,
                "description": "Automatic failover",
                "color": ft.Colors.RED
            },
            {
                "id": "undo_redo",
                "name": "Undo/Redo",
                "icon": ft.Icons.HISTORY,
                "description": "Action history",
                "color": ft.Colors.BLUE
            },
            {
                "id": "ml",
                "name": "ML Predictions",
                "icon": ft.Icons.PSYCHOLOGY,
                "description": "AI-powered insights",
                "color": ft.Colors.PURPLE
            },
            {
                "id": "3d_charts",
                "name": "3D Visualizations",
                "icon": ft.Icons.VIEW_IN_AR,
                "description": "Advanced charts",
                "color": ft.Colors.TEAL
            }
        ]
        
        feature_cards = []
        for i in range(0, len(features), 2):
            row_features = features[i:i+2]
            row = ft.Row([
                self._create_feature_card(feature) for feature in row_features
            ], spacing=10)
            feature_cards.append(row)
        
        return ft.Container(
            content=ft.Column(feature_cards, spacing=10),
            padding=ft.padding.symmetric(vertical=10)
        )
    
    def _create_feature_card(self, feature: Dict[str, Any]) -> ft.Container:
        """Create individual feature status card."""
        is_enabled = self.features_status.get(feature["id"], False)
        
        return ft.Container(
            content=ft.Row([
                ft.Container(
                    content=ft.Icon(feature["icon"], color=ft.Colors.WHITE, size=24),
                    width=50,
                    height=50,
                    bgcolor=feature["color"] if is_enabled else ft.Colors.GREY_400,
                    border_radius=25,
                    alignment=ft.alignment.center
                ),
                ft.Column([
                    ft.Text(feature["name"], size=14, weight=ft.FontWeight.W_600),
                    ft.Text(feature["description"], size=11, color=ft.Colors.GREY_600)
                ], spacing=2, expand=True),
                ft.Switch(
                    value=is_enabled,
                    active_color=feature["color"],
                    on_change=lambda e, fid=feature["id"]: self._handle_feature_toggle(fid, e.control.value)
                )
            ], alignment=ft.MainAxisAlignment.START),
            padding=15,
            bgcolor=ft.Colors.GREY_50 if is_enabled else ft.Colors.GREY_100,
            border_radius=10,
            border=ft.border.all(1, feature["color"] if is_enabled else ft.Colors.GREY_300),
            expand=True
        )
    
    def _create_stats_section(self) -> ft.Container:
        """Create system statistics section."""
        stats_items = [
            ("Cache Hits", self.system_stats.get("cache_hits", 0), ft.Icons.SPEED, ft.Colors.ORANGE),
            ("Operations", self.system_stats.get("total_operations", 0), ft.Icons.FUNCTIONS, ft.Colors.BLUE),
            ("Saved Versions", self.system_stats.get("saved_versions", 0), ft.Icons.SAVE, ft.Colors.GREEN),
            ("ML Predictions", self.system_stats.get("ml_predictions", 0), ft.Icons.PSYCHOLOGY, ft.Colors.PURPLE)
        ]
        
        stats_row = ft.Row([
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(icon, color=color, size=20),
                        ft.Text(str(value), size=20, weight=ft.FontWeight.BOLD, color=color)
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    ft.Text(label, size=11, color=ft.Colors.GREY_600)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
                padding=10,
                bgcolor=ft.Colors.with_opacity(0.1, color),
                border_radius=8,
                expand=True
            )
            for label, value, icon, color in stats_items
        ], spacing=10)
        
        return ft.Container(
            content=ft.Column([
                ft.Text("System Performance", size=14, weight=ft.FontWeight.W_600, color=ft.Colors.GREY_700),
                stats_row
            ], spacing=10),
            padding=ft.padding.symmetric(vertical=10)
        )
    
    def _create_actions_section(self) -> ft.Container:
        """Create quick actions section."""
        actions = [
            ("Clear Cache", ft.Icons.CLEAR_ALL, ft.Colors.ORANGE, "clear_cache"),
            ("Backup Now", ft.Icons.BACKUP, ft.Colors.GREEN, "backup_now"),
            ("View History", ft.Icons.HISTORY, ft.Colors.BLUE, "view_history"),
            ("ML Report", ft.Icons.ASSESSMENT, ft.Colors.PURPLE, "ml_report")
        ]
        
        action_buttons = ft.Row([
            ft.ElevatedButton(
                text=label,
                icon=icon,
                icon_color=color,
                on_click=lambda _, action=action_id: self.on_action(action) if self.on_action else None,
                style=ft.ButtonStyle(
                    color=color,
                    bgcolor={
                        ft.MaterialState.DEFAULT: ft.Colors.with_opacity(0.1, color),
                        ft.MaterialState.HOVERED: ft.Colors.with_opacity(0.2, color)
                    }
                )
            )
            for label, icon, color, action_id in actions
        ], spacing=10, wrap=True)
        
        return ft.Container(
            content=ft.Column([
                ft.Text("Quick Actions", size=14, weight=ft.FontWeight.W_600, color=ft.Colors.GREY_700),
                action_buttons
            ], spacing=10),
            padding=ft.padding.only(top=10)
        )
    
    def _handle_feature_toggle(self, feature_id: str, enabled: bool):
        """Handle feature toggle."""
        self.features_status[feature_id] = enabled
        if self.on_feature_toggle:
            self.on_feature_toggle(feature_id, enabled)
    
    def update_status(self, features_status: Dict[str, bool], system_stats: Dict[str, Any]):
        """Update panel with current status."""
        self.features_status = features_status
        self.system_stats = system_stats
        # Note: In a real Flet app, you'd need to trigger a page update here


class FeatureStatusIndicator:
    """Small indicator widget for feature status."""
    
    def __init__(self, feature_name: str, is_active: bool = False, color: str = ft.Colors.GREEN):
        self.feature_name = feature_name
        self.is_active = is_active
        self.color = color
    
    def build(self) -> ft.Container:
        """Build the status indicator."""
        return ft.Container(
            content=ft.Row([
                ft.Container(
                    width=8,
                    height=8,
                    bgcolor=self.color if self.is_active else ft.Colors.GREY_400,
                    border_radius=4
                ),
                ft.Text(
                    self.feature_name,
                    size=12,
                    color=self.color if self.is_active else ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_500 if self.is_active else ft.FontWeight.NORMAL
                )
            ], spacing=5),
            tooltip=f"{self.feature_name} is {'active' if self.is_active else 'inactive'}"
        )


class MLInsightsWidget:
    """Widget for displaying ML-powered insights."""
    
    def __init__(self, insights: Optional[Dict[str, Any]] = None):
        self.insights = insights or {}
    
    def build(self) -> ft.Container:
        """Build the ML insights widget."""
        if not self.insights:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.PSYCHOLOGY, color=ft.Colors.PURPLE_200, size=40),
                    ft.Text("ML insights will appear here", 
                           size=14, color=ft.Colors.GREY_500, text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                padding=20,
                bgcolor=ft.Colors.PURPLE_50,
                border_radius=10,
                alignment=ft.alignment.center
            )
        
        predictions = self.insights.get("predictions", {})
        recommendations = self.insights.get("recommendations", [])
        confidence = self.insights.get("confidence_score", 0)
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.PSYCHOLOGY, color=ft.Colors.PURPLE_600, size=24),
                    ft.Text("AI-Powered Insights", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_900),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text(f"{confidence:.0%}", size=12, color=ft.Colors.WHITE),
                        padding=ft.padding.symmetric(horizontal=10, vertical=5),
                        bgcolor=ft.Colors.PURPLE_600,
                        border_radius=15
                    )
                ]),
                ft.Divider(height=20, color=ft.Colors.PURPLE_100),
                
                # Predictions
                ft.Text("Predicted Outcomes", size=14, weight=ft.FontWeight.W_600, color=ft.Colors.GREY_700),
                ft.Container(
                    content=ft.Column([
                        self._create_prediction_row(metric, value)
                        for metric, value in predictions.items()
                    ], spacing=8),
                    padding=10,
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8
                ),
                
                # Recommendations
                ft.Text("AI Recommendations", size=14, weight=ft.FontWeight.W_600, color=ft.Colors.GREY_700),
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.LIGHTBULB, color=ft.Colors.AMBER_600, size=16),
                            ft.Text(rec, size=12, color=ft.Colors.GREY_700, expand=True)
                        ], spacing=10)
                        for rec in recommendations[:3]
                    ], spacing=8),
                    padding=10,
                    bgcolor=ft.Colors.AMBER_50,
                    border_radius=8
                )
            ], spacing=10),
            padding=20,
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.PURPLE_200),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.08, ft.Colors.PURPLE),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_prediction_row(self, metric: str, value: Dict[str, Any]) -> ft.Row:
        """Create a prediction row."""
        predicted = value.get("predicted", 0)
        confidence_interval = value.get("confidence_interval", [0, 0])
        
        return ft.Row([
            ft.Text(metric.replace("_", " ").title(), size=12, color=ft.Colors.GREY_600, expand=True),
            ft.Text(f"{predicted:.2f}", size=12, weight=ft.FontWeight.W_600, color=ft.Colors.PURPLE_700),
            ft.Text(f"[{confidence_interval[0]:.2f}, {confidence_interval[1]:.2f}]", 
                   size=11, color=ft.Colors.GREY_500)
        ])
    
    def update_insights(self, insights: Dict[str, Any]):
        """Update the insights data."""
        self.insights = insights 