"""
Enhanced Integration Service
============================

Central coordination service that integrates all advanced features:
- Data persistence with versioning
- Performance caching
- Error recovery
- Undo/redo functionality
- ML predictions
- Advanced 3D visualizations
"""

import logging
import threading
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

# Import services - with fallbacks if not available
try:
    from services.persistence_service import DataPersistenceService
except ImportError:
    DataPersistenceService = None

try:
    from services.cache_service import PerformanceCacheService
except ImportError:
    PerformanceCacheService = None

try:
    from services.recovery_service import ErrorRecoveryService
except ImportError:
    ErrorRecoveryService = None

try:
    from services.undo_redo_service import UndoRedoService, StateChangeCommand
except ImportError:
    UndoRedoService = None
    StateChangeCommand = None

try:
    from services.ml_prediction_service import MLPredictionService, PredictionTarget
except ImportError:
    MLPredictionService = None
    PredictionTarget = None

try:
    from components.charts.advanced_3d_charts import Advanced3DChartsService
except ImportError:
    Advanced3DChartsService = None


class OperationStatus(Enum):
    """Status of operations."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CACHED = "cached"


@dataclass
class OperationContext:
    """Context for tracking operations."""
    operation_id: str
    operation_type: str
    status: OperationStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    progress: float = 0.0
    error_message: Optional[str] = None
    cache_hit: bool = False


class EnhancedIntegrationService:
    """Central service integrating all advanced features."""
    
    def __init__(self, 
                 enable_persistence: bool = True,
                 enable_caching: bool = True,
                 enable_recovery: bool = True,
                 enable_undo_redo: bool = True,
                 enable_ml: bool = True,
                 enable_3d_charts: bool = True):
        
        self.logger = logging.getLogger(__name__)
        self.lock = threading.RLock()
        
        # Feature flags
        self.features = {
            'persistence': enable_persistence and DataPersistenceService is not None,
            'caching': enable_caching and PerformanceCacheService is not None,
            'recovery': enable_recovery and ErrorRecoveryService is not None,
            'undo_redo': enable_undo_redo and UndoRedoService is not None,
            'ml': enable_ml and MLPredictionService is not None,
            '3d_charts': enable_3d_charts and Advanced3DChartsService is not None
        }
        
        # Initialize services
        self._initialize_services()
        
        # Operation tracking
        self.active_operations: Dict[str, OperationContext] = {}
        self.operation_history: List[OperationContext] = []
        
        # System status
        self.system_status = {
            'initialization_time': datetime.now(),
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'cache_hits': 0
        }

        # Project versioning
        self.current_project_version: Optional[str] = None
        
        enabled_features = [k for k, v in self.features.items() if v]
        self.logger.info(f"Enhanced Integration Service initialized with: {', '.join(enabled_features)}")
    
    def _initialize_services(self):
        """Initialize all available services."""
        try:
            # Data persistence
            if self.features['persistence']:
                self.persistence_service = DataPersistenceService()
            else:
                self.persistence_service = None
            
            # Performance caching
            if self.features['caching']:
                self.cache_service = PerformanceCacheService(
                    use_redis=False,
                    lru_max_size=2000,
                    default_ttl=1800  # 30 minutes
                )
            else:
                self.cache_service = None
            
            # Error recovery
            if self.features['recovery']:
                self.recovery_service = ErrorRecoveryService()
            else:
                self.recovery_service = None
            
            # Undo/redo
            if self.features['undo_redo']:
                self.undo_redo_service = UndoRedoService(
                    max_history_size=200,
                    max_memory_mb=100
                )
            else:
                self.undo_redo_service = None
            
            # ML predictions
            if self.features['ml']:
                self.ml_service = MLPredictionService()
            else:
                self.ml_service = None
            
            # 3D charts
            if self.features['3d_charts']:
                self.charts_3d_service = Advanced3DChartsService()
            else:
                self.charts_3d_service = None
            
            self.logger.info("Services initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize services: {e}")
            raise
    
    def run_enhanced_financial_model(self, 
                                   project_data: Dict[str, Any],
                                   include_ml_predictions: bool = True,
                                   include_monte_carlo: bool = True) -> Dict[str, Any]:
        """Run comprehensive financial model with all enhancements."""
        operation_id = self._start_operation("enhanced_financial_model")
        
        try:
            # Check cache first
            cache_key = f"financial_model:{hash(str(project_data))}"
            if self.cache_service:
                cached_result, found = self.cache_service.get(cache_key)
                if found:
                    self.system_status['cache_hits'] += 1
                    self._complete_operation(operation_id, cached_result, cache_hit=True)
                    return cached_result
            
            self._update_progress(operation_id, 0.1, "Running base financial model")
            
            # Run base financial model (simplified for demo)
            base_results = {
                'kpis': {
                    'irr_equity': 0.12,
                    'npv_equity': 1000000,
                    'lcoe': 0.06,
                    'payback_years': 10
                },
                'cashflow': {},
                'calculations': {}
            }
            
            self._update_progress(operation_id, 0.4, "Running ML predictions")
            
            # Add ML predictions if enabled
            ml_results = {}
            if include_ml_predictions and self.ml_service:
                try:
                    # Convert project data to assumptions format
                    assumptions = project_data.get('assumptions', {})
                    
                    # Get ML predictions
                    predictions = self.ml_service.predict_multiple(assumptions)
                    
                    ml_results = {
                        'predictions': {
                            target.value: {
                                'predicted_value': result.predicted_value,
                                'confidence_interval': result.confidence_interval,
                                'recommendations': result.recommendations
                            }
                            for target, result in predictions.items()
                        },
                        'risk_assessment': self.ml_service.risk_assessment(assumptions),
                        'benchmark_comparison': self.ml_service.benchmark_comparison(assumptions)
                    }
                    
                except Exception as e:
                    self.logger.warning(f"ML predictions failed: {e}")
                    ml_results = {'error': str(e)}
            
            self._update_progress(operation_id, 0.7, "Running Monte Carlo simulation")
            
            # Add Monte Carlo if enabled
            monte_carlo_results = {}
            if include_monte_carlo:
                try:
                    monte_carlo_results = self._run_monte_carlo_simulation(project_data, 1000)
                except Exception as e:
                    self.logger.warning(f"Monte Carlo simulation failed: {e}")
                    monte_carlo_results = {'error': str(e)}
            
            self._update_progress(operation_id, 0.9, "Compiling results")
            
            # Compile comprehensive results
            enhanced_results = {
                **base_results,
                'ml_predictions': ml_results,
                'monte_carlo': monte_carlo_results,
                'enhanced_kpis': self._calculate_enhanced_kpis(base_results, ml_results),
                'model_metadata': {
                    'calculation_time': datetime.now().isoformat(),
                    'model_version': '3.0',
                    'features_used': [k for k, v in self.features.items() if v]
                }
            }
            
            # Cache results
            if self.cache_service:
                self.cache_service.put(cache_key, enhanced_results, ttl=1800)
            
            self._complete_operation(operation_id, enhanced_results)
            return enhanced_results
            
        except Exception as e:
            self._fail_operation(operation_id, str(e))
            
            # Try recovery if available
            if self.recovery_service:
                try:
                    return self._get_fallback_results()
                except:
                    pass
            
            raise
    
    def generate_advanced_charts(self, 
                               financial_results: Dict[str, Any],
                               project_name: str = "Project Analysis") -> Dict[str, str]:
        """Generate comprehensive chart suite including 3D visualizations."""
        operation_id = self._start_operation("generate_charts")
        
        try:
            charts = {}
            
            if self.charts_3d_service:
                self._update_progress(operation_id, 0.2, "Creating 3D visualizations")
                
                # 3D Scenario Comparison
                if 'ml_predictions' in financial_results:
                    scenarios_data = {
                        'Base Case': financial_results.get('kpis', {}),
                        'Optimistic': {k: v * 1.1 for k, v in financial_results.get('kpis', {}).items()},
                        'Pessimistic': {k: v * 0.9 for k, v in financial_results.get('kpis', {}).items()}
                    }
                    
                    charts['3d_scenario_comparison'] = \
                        self.charts_3d_service.create_scenario_comparison_3d(
                            scenarios_data, f"{project_name} - 3D Scenario Analysis"
                        )
                
                # 3D Monte Carlo
                if 'monte_carlo' in financial_results:
                    mc_data = financial_results['monte_carlo']
                    charts['3d_monte_carlo'] = \
                        self.charts_3d_service.create_monte_carlo_distribution_3d(
                            mc_data, f"{project_name} - Monte Carlo 3D"
                        )
                
                # 3D Risk Analysis
                if 'ml_predictions' in financial_results:
                    risk_data = financial_results['ml_predictions'].get('risk_assessment', {})
                    if risk_data:
                        charts['3d_risk_analysis'] = \
                            self.charts_3d_service.create_risk_analysis_3d(
                                risk_data, f"{project_name} - Risk Analysis 3D"
                            )
            
            self._update_progress(operation_id, 0.8, "Adding standard charts")
            
            # Add standard charts (placeholder)
            charts.update({
                'cashflow_chart': "<!-- 2D Cashflow Chart -->",
                'kpi_dashboard': "<!-- KPI Dashboard -->",
                'sensitivity_chart': "<!-- 2D Sensitivity Chart -->"
            })
            
            self._complete_operation(operation_id, charts)
            return charts
            
        except Exception as e:
            self._fail_operation(operation_id, str(e))
            return {'error_message': f"Chart generation failed: {e}"}
    
    def save_project_with_versioning(self, project_id: str, project_data: Dict[str, Any]) -> str:
        """Save project with automatic versioning."""
        operation_id = self._start_operation("save_project")
        
        try:
            if not self.persistence_service:
                raise ValueError("Persistence service not available")
            
            # Save project version
            version_id = self.persistence_service.save_project_version(
                project_id, project_data, "Auto-save"
            )

            # Update current project version
            old_version = self.current_project_version
            self.current_project_version = version_id

            # Invalidate cache
            if self.cache_service:
                self.cache_service.invalidate_prefix(f"financial_model:{project_id}")

            # Record undo command
            if self.undo_redo_service and StateChangeCommand:
                command = StateChangeCommand(
                    target=self,
                    property_name="current_project_version",
                    old_value=old_version,
                    new_value=version_id,
                    description=f"Save project version {version_id}"
                )
                self.undo_redo_service.execute_command(command)
            
            self._complete_operation(operation_id, {'version_id': version_id})
            return version_id
            
        except Exception as e:
            self._fail_operation(operation_id, str(e))
            raise
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        with self.lock:
            status = {
                'integration_service': {
                    **self.system_status,
                    'active_operations': len(self.active_operations),
                    'features_enabled': self.features
                }
            }
            
            # Add service-specific status
            if self.cache_service:
                status['cache_service'] = self.cache_service.get_stats()
            
            if self.recovery_service:
                status['recovery_service'] = self.recovery_service.get_recovery_stats()
            
            if self.undo_redo_service:
                status['undo_redo_service'] = self.undo_redo_service.get_statistics()
            
            if self.ml_service:
                status['ml_service'] = self.ml_service.get_model_statistics()
            
            if self.persistence_service:
                status['persistence_service'] = self.persistence_service.get_statistics()
            
            return status
    
    def _start_operation(self, operation_type: str) -> str:
        """Start tracking an operation."""
        operation_id = f"{operation_type}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        with self.lock:
            context = OperationContext(
                operation_id=operation_id,
                operation_type=operation_type,
                status=OperationStatus.IN_PROGRESS,
                start_time=datetime.now()
            )
            
            self.active_operations[operation_id] = context
            self.system_status['total_operations'] += 1
        
        return operation_id
    
    def _update_progress(self, operation_id: str, progress: float, message: str = ""):
        """Update operation progress."""
        with self.lock:
            if operation_id in self.active_operations:
                self.active_operations[operation_id].progress = progress
    
    def _complete_operation(self, operation_id: str, result_data: Any, cache_hit: bool = False):
        """Mark operation as completed."""
        with self.lock:
            if operation_id in self.active_operations:
                context = self.active_operations[operation_id]
                context.status = OperationStatus.COMPLETED
                context.end_time = datetime.now()
                context.progress = 1.0
                context.cache_hit = cache_hit
                
                # Move to history
                self.operation_history.append(context)
                del self.active_operations[operation_id]
                
                self.system_status['successful_operations'] += 1
                if cache_hit:
                    self.system_status['cache_hits'] += 1
    
    def _fail_operation(self, operation_id: str, error_message: str):
        """Mark operation as failed."""
        with self.lock:
            if operation_id in self.active_operations:
                context = self.active_operations[operation_id]
                context.status = OperationStatus.FAILED
                context.end_time = datetime.now()
                context.error_message = error_message
                
                # Move to history
                self.operation_history.append(context)
                del self.active_operations[operation_id]
                
                self.system_status['failed_operations'] += 1
    
    def _run_monte_carlo_simulation(self, project_data: Dict[str, Any], iterations: int) -> Dict[str, Any]:
        """Run Monte Carlo simulation."""
        import numpy as np
        
        # Simplified Monte Carlo
        results = {
            'irr_equity': np.random.normal(0.12, 0.02, iterations),
            'npv_equity': np.random.normal(1000000, 200000, iterations),
            'lcoe': np.random.normal(0.06, 0.01, iterations)
        }
        
        return {
            'results': results,
            'statistics': {
                'irr_equity': {
                    'mean': float(np.mean(results['irr_equity'])),
                    'std': float(np.std(results['irr_equity'])),
                    'percentiles': {
                        'p10': float(np.percentile(results['irr_equity'], 10)),
                        'p50': float(np.percentile(results['irr_equity'], 50)),
                        'p90': float(np.percentile(results['irr_equity'], 90))
                    }
                }
            },
            'iterations': iterations
        }
    
    def _calculate_enhanced_kpis(self, base_results: Dict[str, Any], ml_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate enhanced KPIs combining base and ML results."""
        enhanced_kpis = base_results.get('kpis', {}).copy()
        
        # Add ML-derived KPIs
        if 'predictions' in ml_results:
            predictions = ml_results['predictions']
            enhanced_kpis.update({
                'ml_irr_prediction': predictions.get('irr_equity', {}).get('predicted_value', 0),
                'ml_npv_prediction': predictions.get('npv_equity', {}).get('predicted_value', 0),
                'ml_confidence_score': 0.8
            })
        
        if 'risk_assessment' in ml_results:
            risk_data = ml_results['risk_assessment']
            enhanced_kpis.update({
                'overall_risk_score': risk_data.get('risk_score', 2.0),
                'risk_level': risk_data.get('overall_risk', 'MEDIUM')
            })
        
        return enhanced_kpis
    
    def _get_fallback_results(self) -> Dict[str, Any]:
        """Get fallback results when calculation fails."""
        return {
            'kpis': {
                'irr_equity': 0.10,
                'npv_equity': 500000,
                'lcoe': 0.07,
                'payback_years': 12
            },
            'status': 'fallback',
            'message': 'Using fallback data due to calculation failure'
        }


# Global service instance
_enhanced_integration_service: Optional[EnhancedIntegrationService] = None

def get_integration_service() -> EnhancedIntegrationService:
    """Get global enhanced integration service instance."""
    global _enhanced_integration_service
    if _enhanced_integration_service is None:
        _enhanced_integration_service = EnhancedIntegrationService()
    return _enhanced_integration_service


def initialize_integration_service(**kwargs) -> EnhancedIntegrationService:
    """Initialize global integration service with custom settings."""
    global _enhanced_integration_service
    _enhanced_integration_service = EnhancedIntegrationService(**kwargs)
    return _enhanced_integration_service